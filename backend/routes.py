"""
API路由定义
提供RESTful接口
"""

from flask import Blueprint, request, jsonify
from models import db, Agent, LLMConfig, Discussion, Message, AppSettings, UserPreferences
import json
import logging
import os
import sys

logger = logging.getLogger(__name__)

api_bp = Blueprint('api', __name__)

# ============= LLM配置相关接口 =============

@api_bp.route('/llm-configs', methods=['GET'])
def get_llm_configs():
    """获取所有LLM配置"""
    try:
        configs = LLMConfig.query.all()
        return jsonify([config.to_dict() for config in configs])
    except Exception as e:
        logger.error(f"Error getting LLM configs: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/llm-configs', methods=['POST'])
def create_llm_config():
    """创建LLM配置"""
    try:
        data = request.get_json()
        
        config = LLMConfig(
            name=data['name'],
            provider=data['provider'],
            model=data['model'],
            api_key=data['apiKey'],
            base_url=data.get('baseURL'),
            temperature=data.get('temperature', 0.7),
            max_tokens=data.get('maxTokens', 1000),
            system_prompt=data.get('systemPrompt')
        )
        
        db.session.add(config)
        db.session.commit()
        
        return jsonify(config.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error creating LLM config: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/llm-configs/<config_id>', methods=['PUT'])
def update_llm_config(config_id):
    """更新LLM配置"""
    try:
        config = LLMConfig.query.get_or_404(config_id)
        data = request.get_json()
        
        config.name = data.get('name', config.name)
        config.provider = data.get('provider', config.provider)
        config.model = data.get('model', config.model)
        config.api_key = data.get('apiKey', config.api_key)
        config.base_url = data.get('baseURL', config.base_url)
        config.temperature = data.get('temperature', config.temperature)
        config.max_tokens = data.get('maxTokens', config.max_tokens)
        config.system_prompt = data.get('systemPrompt', config.system_prompt)
        
        db.session.commit()
        
        return jsonify(config.to_dict())
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating LLM config: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/llm-configs/<config_id>', methods=['DELETE'])
def delete_llm_config(config_id):
    """删除LLM配置"""
    try:
        config = LLMConfig.query.get_or_404(config_id)

        # 检查是否有智能体在使用此配置
        agents_using_config = Agent.query.filter_by(llm_config_id=config_id).all()
        if agents_using_config:
            agent_names = [agent.name for agent in agents_using_config]
            return jsonify({
                'error': f'无法删除正在使用的LLM配置，以下智能体正在使用: {", ".join(agent_names)}',
                'agents': [{'id': agent.id, 'name': agent.name} for agent in agents_using_config]
            }), 400

        db.session.delete(config)
        db.session.commit()

        return jsonify({'message': 'LLM配置已删除'})
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting LLM config: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/llm-configs/<config_id>/force-delete', methods=['DELETE'])
def force_delete_llm_config(config_id):
    """强制删除LLM配置（会先删除相关智能体）"""
    try:
        config = LLMConfig.query.get_or_404(config_id)

        # 获取使用此配置的智能体
        agents_using_config = Agent.query.filter_by(llm_config_id=config_id).all()

        if agents_using_config:
            logger.info(f"强制删除LLM配置 {config_id}，将同时删除 {len(agents_using_config)} 个智能体")

            # 删除相关智能体（会级联删除相关消息）
            for agent in agents_using_config:
                db.session.delete(agent)

        # 删除LLM配置
        db.session.delete(config)
        db.session.commit()

        return jsonify({
            'message': 'LLM配置已强制删除',
            'deletedAgents': len(agents_using_config)
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error force deleting LLM config: {e}")
        return jsonify({'error': str(e)}), 500

# ============= 智能体相关接口 =============

@api_bp.route('/agents', methods=['GET'])
def get_agents():
    """获取所有智能体"""
    try:
        agents = Agent.query.all()
        return jsonify([agent.to_dict() for agent in agents])
    except Exception as e:
        logger.error(f"Error getting agents: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/agents', methods=['POST'])
def create_agent():
    """创建智能体"""
    try:
        data = request.get_json()
        
        # 验证LLM配置是否存在
        llm_config = LLMConfig.query.get(data['llmConfig']['id'])
        if not llm_config:
            return jsonify({'error': 'LLM配置不存在'}), 400
        
        agent = Agent(
            name=data['name'],
            avatar=data['avatar'],
            expertise=json.dumps(data['expertise']),
            thinking_style=data['thinkingStyle'],
            personality=data['personality'],
            tools=json.dumps(data['tools']),
            is_active=data.get('isActive', True),
            is_moderator=data.get('isModerator', False),
            moderator_config=json.dumps(data['moderatorConfig']) if data.get('moderatorConfig') else None,
            llm_config_id=data['llmConfig']['id']
        )
        
        db.session.add(agent)
        db.session.commit()
        
        return jsonify(agent.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error creating agent: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/agents/<agent_id>', methods=['PUT'])
def update_agent(agent_id):
    """更新智能体"""
    try:
        agent = Agent.query.get_or_404(agent_id)
        data = request.get_json()
        
        # 如果更新了LLM配置，验证其存在性
        if 'llmConfig' in data:
            llm_config = LLMConfig.query.get(data['llmConfig']['id'])
            if not llm_config:
                return jsonify({'error': 'LLM配置不存在'}), 400
            agent.llm_config_id = data['llmConfig']['id']
        
        agent.name = data.get('name', agent.name)
        agent.avatar = data.get('avatar', agent.avatar)
        agent.expertise = json.dumps(data.get('expertise', json.loads(agent.expertise)))
        agent.thinking_style = data.get('thinkingStyle', agent.thinking_style)
        agent.personality = data.get('personality', agent.personality)
        agent.tools = json.dumps(data.get('tools', json.loads(agent.tools)))
        agent.is_active = data.get('isActive', agent.is_active)
        agent.is_moderator = data.get('isModerator', agent.is_moderator)
        agent.moderator_config = json.dumps(data['moderatorConfig']) if data.get('moderatorConfig') else agent.moderator_config
        
        db.session.commit()
        
        return jsonify(agent.to_dict())
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating agent: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/agents/<agent_id>', methods=['DELETE'])
def delete_agent(agent_id):
    """删除智能体"""
    try:
        agent = Agent.query.get_or_404(agent_id)

        # 检查是否有相关消息
        message_count = Message.query.filter_by(agent_id=agent_id).count()
        if message_count > 0:
            logger.info(f"删除智能体 {agent_id}，将级联删除 {message_count} 条消息")

        db.session.delete(agent)
        db.session.commit()

        return jsonify({
            'message': '智能体已删除',
            'deletedMessages': message_count
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting agent: {e}")
        return jsonify({'error': str(e)}), 500

# ============= 讨论相关接口 =============

@api_bp.route('/discussions', methods=['GET'])
def get_discussions():
    """获取所有讨论"""
    try:
        discussions = Discussion.query.order_by(Discussion.created_at.desc()).all()
        return jsonify([discussion.to_dict() for discussion in discussions])
    except Exception as e:
        logger.error(f"Error getting discussions: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/discussions', methods=['POST'])
def create_discussion():
    """创建讨论"""
    try:
        data = request.get_json()
        
        # 检查是否已存在相同ID的讨论
        existing_discussion = Discussion.query.filter_by(id=data.get('id')).first()
        if existing_discussion:
            # 如果讨论已存在，更新而不是创建新的
            existing_discussion.topic = data.get('topic', existing_discussion.topic)
            existing_discussion.mode = data.get('mode', existing_discussion.mode)
            existing_discussion.participants = json.dumps(data.get('participants', json.loads(existing_discussion.participants)))
            existing_discussion.status = data.get('status', existing_discussion.status)
            existing_discussion.consensus = data.get('consensus', existing_discussion.consensus)
            existing_discussion.consensus_score = data.get('consensusScore', existing_discussion.consensus_score)
            
            # 处理消息
            if 'messages' in data:
                # 删除现有消息
                Message.query.filter_by(discussion_id=existing_discussion.id).delete()
                # 添加新消息
                for msg_data in data['messages']:
                    # 验证必需字段
                    if not msg_data.get('agentId'):
                        logger.warning(f"跳过消息 {msg_data.get('id', 'unknown')}：缺少agentId")
                        continue

                    try:
                        message = Message(
                            id=msg_data.get('id'),
                            agent_id=msg_data['agentId'],
                            discussion_id=existing_discussion.id,
                            content=msg_data['content'],
                            message_type=msg_data['type'],
                            reply_to=msg_data.get('replyTo')
                        )
                        db.session.add(message)
                    except ValueError as e:
                        logger.warning(f"跳过无效消息 {msg_data.get('id', 'unknown')}: {str(e)}")
                        continue
            
            db.session.commit()
            return jsonify(existing_discussion.to_dict()), 200

        discussion = Discussion(
            id=data.get('id'),
            topic=data['topic'],
            mode=data['mode'],
            participants=json.dumps(data['participants']),
            status=data.get('status', 'active'),
            consensus=data.get('consensus'),
            consensus_score=data.get('consensusScore', 0.0),
            moderator_id=data.get('moderatorId'),
            moderator_summaries=json.dumps(data.get('moderatorSummaries', [])),
            topic_relevance_score=data.get('topicRelevanceScore', 1.0),
            moderator_interventions=data.get('moderatorInterventions', 0)
        )

        db.session.add(discussion)
        
        # 处理消息
        if 'messages' in data:
            for msg_data in data['messages']:
                # 验证必需字段
                if not msg_data.get('agentId'):
                    logger.warning(f"跳过消息 {msg_data.get('id', 'unknown')}：缺少agentId")
                    continue

                try:
                    message = Message(
                        id=msg_data.get('id'),
                        agent_id=msg_data['agentId'],
                        discussion_id=discussion.id,
                        content=msg_data['content'],
                        message_type=msg_data['type'],
                        reply_to=msg_data.get('replyTo')
                    )
                    db.session.add(message)
                except ValueError as e:
                    logger.warning(f"跳过无效消息 {msg_data.get('id', 'unknown')}: {str(e)}")
                    continue

        db.session.commit()
        return jsonify(discussion.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error creating discussion: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/discussions/<discussion_id>', methods=['PUT'])
def update_discussion(discussion_id):
    """更新讨论"""
    try:
        discussion = Discussion.query.get_or_404(discussion_id)
        data = request.get_json()

        discussion.topic = data.get('topic', discussion.topic)
        discussion.mode = data.get('mode', discussion.mode)
        discussion.participants = json.dumps(data.get('participants', json.loads(discussion.participants)))
        discussion.status = data.get('status', discussion.status)
        discussion.consensus = data.get('consensus', discussion.consensus)
        discussion.consensus_score = data.get('consensusScore', discussion.consensus_score)
        discussion.moderator_id = data.get('moderatorId', discussion.moderator_id)
        discussion.moderator_summaries = json.dumps(data.get('moderatorSummaries', json.loads(discussion.moderator_summaries) if discussion.moderator_summaries else []))
        discussion.topic_relevance_score = data.get('topicRelevanceScore', discussion.topic_relevance_score)
        discussion.moderator_interventions = data.get('moderatorInterventions', discussion.moderator_interventions)

        db.session.commit()

        return jsonify(discussion.to_dict())
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating discussion: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/discussions/<discussion_id>', methods=['DELETE'])
def delete_discussion(discussion_id):
    """删除讨论"""
    try:
        discussion = Discussion.query.get_or_404(discussion_id)

        # 检查是否有相关消息
        message_count = Message.query.filter_by(discussion_id=discussion_id).count()
        if message_count > 0:
            logger.info(f"删除讨论 {discussion_id}，将级联删除 {message_count} 条消息")

        db.session.delete(discussion)
        db.session.commit()

        return jsonify({
            'message': '讨论已删除',
            'deletedMessages': message_count
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting discussion: {e}")
        return jsonify({'error': str(e)}), 500

# ============= 批量删除接口 =============

@api_bp.route('/agents/batch-delete', methods=['DELETE'])
def batch_delete_agents():
    """批量删除智能体"""
    try:
        data = request.get_json()
        agent_ids = data.get('agentIds', [])

        if not agent_ids:
            return jsonify({'error': '未提供要删除的智能体ID'}), 400

        deleted_count = 0
        total_messages_deleted = 0

        for agent_id in agent_ids:
            agent = Agent.query.get(agent_id)
            if agent:
                # 统计相关消息数量
                message_count = Message.query.filter_by(agent_id=agent_id).count()
                total_messages_deleted += message_count

                db.session.delete(agent)
                deleted_count += 1
                logger.info(f"删除智能体 {agent_id}，级联删除 {message_count} 条消息")

        db.session.commit()

        return jsonify({
            'message': f'成功删除 {deleted_count} 个智能体',
            'deletedAgents': deleted_count,
            'deletedMessages': total_messages_deleted
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error batch deleting agents: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/discussions/batch-delete', methods=['DELETE'])
def batch_delete_discussions():
    """批量删除讨论"""
    try:
        data = request.get_json()
        discussion_ids = data.get('discussionIds', [])

        if not discussion_ids:
            return jsonify({'error': '未提供要删除的讨论ID'}), 400

        deleted_count = 0
        total_messages_deleted = 0

        for discussion_id in discussion_ids:
            discussion = Discussion.query.get(discussion_id)
            if discussion:
                # 统计相关消息数量
                message_count = Message.query.filter_by(discussion_id=discussion_id).count()
                total_messages_deleted += message_count

                db.session.delete(discussion)
                deleted_count += 1
                logger.info(f"删除讨论 {discussion_id}，级联删除 {message_count} 条消息")

        db.session.commit()

        return jsonify({
            'message': f'成功删除 {deleted_count} 个讨论',
            'deletedDiscussions': deleted_count,
            'deletedMessages': total_messages_deleted
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error batch deleting discussions: {e}")
        return jsonify({'error': str(e)}), 500

# ============= 消息相关接口 =============

@api_bp.route('/discussions/<discussion_id>/messages', methods=['POST'])
def add_message(discussion_id):
    """添加消息到讨论"""
    try:
        data = request.get_json()

        # 验证讨论是否存在
        discussion = Discussion.query.get_or_404(discussion_id)

        # 检查消息是否已存在（避免重复添加）
        existing_message = Message.query.filter_by(
            id=data.get('id'),
            discussion_id=discussion_id
        ).first()
        
        if existing_message:
            # 消息已存在，返回现有消息
            return jsonify(existing_message.to_dict()), 200

        # 验证必需字段
        if not data.get('agentId'):
            return jsonify({'error': '缺少agentId字段'}), 400

        try:
            message = Message(
                id=data.get('id'),  # 使用前端提供的ID
                agent_id=data['agentId'],
                discussion_id=discussion_id,
                content=data['content'],
                message_type=data['type'],
                reply_to=data.get('replyTo')
            )
        except ValueError as e:
            return jsonify({'error': f'消息数据验证失败: {str(e)}'}), 400

        db.session.add(message)
        db.session.commit()

        return jsonify(message.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error adding message: {e}")
        return jsonify({'error': str(e)}), 500

# ============= 设置相关接口 =============

@api_bp.route('/settings', methods=['GET'])
def get_settings():
    """获取应用设置"""
    try:
        settings = AppSettings.query.first()
        if not settings:
            # 创建默认设置
            settings = AppSettings()
            db.session.add(settings)
            db.session.commit()

        return jsonify(settings.to_dict())
    except Exception as e:
        logger.error(f"Error getting settings: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/settings', methods=['PUT'])
def update_settings():
    """更新应用设置"""
    try:
        settings = AppSettings.query.first()
        if not settings:
            settings = AppSettings()
            db.session.add(settings)

        data = request.get_json()

        settings.version = data.get('version', settings.version)
        settings.auto_save = data.get('autoSave', settings.auto_save)
        settings.max_stored_discussions = data.get('maxStoredDiscussions', settings.max_stored_discussions)
        settings.default_discussion_mode = data.get('defaultDiscussionMode', settings.default_discussion_mode)
        settings.theme = data.get('theme', settings.theme)

        db.session.commit()

        return jsonify(settings.to_dict())
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating settings: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/preferences', methods=['GET'])
def get_preferences():
    """获取用户偏好"""
    try:
        preferences = UserPreferences.query.first()
        if not preferences:
            # 创建默认偏好
            preferences = UserPreferences()
            db.session.add(preferences)
            db.session.commit()

        return jsonify(preferences.to_dict())
    except Exception as e:
        logger.error(f"Error getting preferences: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/preferences', methods=['PUT'])
def update_preferences():
    """更新用户偏好"""
    try:
        preferences = UserPreferences.query.first()
        if not preferences:
            preferences = UserPreferences()
            db.session.add(preferences)

        data = request.get_json()

        preferences.default_agent_count = data.get('defaultAgentCount', preferences.default_agent_count)
        preferences.preferred_llm_provider = data.get('preferredLLMProvider', preferences.preferred_llm_provider)
        preferences.auto_start_discussion = data.get('autoStartDiscussion', preferences.auto_start_discussion)
        preferences.show_advanced_options = data.get('showAdvancedOptions', preferences.show_advanced_options)
        preferences.notifications_enabled = data.get('notificationsEnabled', preferences.notifications_enabled)
        preferences.export_format = data.get('exportFormat', preferences.export_format)

        db.session.commit()

        return jsonify(preferences.to_dict())
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating preferences: {e}")
        return jsonify({'error': str(e)}), 500

# ============= 数据管理接口 =============

@api_bp.route('/data/export', methods=['GET'])
def export_data():
    """导出所有数据"""
    try:
        agents = Agent.query.all()
        llm_configs = LLMConfig.query.all()
        discussions = Discussion.query.all()
        settings = AppSettings.query.first()
        preferences = UserPreferences.query.first()

        data = {
            'agents': [agent.to_dict() for agent in agents],
            'llmConfigs': [config.to_dict() for config in llm_configs],
            'discussions': [discussion.to_dict() for discussion in discussions],
            'settings': settings.to_dict() if settings else {},
            'preferences': preferences.to_dict() if preferences else {}
        }

        return jsonify(data)
    except Exception as e:
        logger.error(f"Error exporting data: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/data/import', methods=['POST'])
def import_data():
    """导入数据"""
    try:
        data = request.get_json()

        # 清除现有数据（可选）
        if data.get('clearExisting', False):
            Message.query.delete()
            Discussion.query.delete()
            Agent.query.delete()
            LLMConfig.query.delete()
            AppSettings.query.delete()
            UserPreferences.query.delete()

        # 导入LLM配置
        if 'llmConfigs' in data:
            for config_data in data['llmConfigs']:
                config = LLMConfig(
                    id=config_data['id'],
                    name=config_data['name'],
                    provider=config_data['provider'],
                    model=config_data['model'],
                    api_key=config_data['apiKey'],
                    base_url=config_data.get('baseURL'),
                    temperature=config_data.get('temperature', 0.7),
                    max_tokens=config_data.get('maxTokens', 1000),
                    system_prompt=config_data.get('systemPrompt')
                )
                db.session.merge(config)

        # 导入智能体
        if 'agents' in data:
            for agent_data in data['agents']:
                agent = Agent(
                    id=agent_data['id'],
                    name=agent_data['name'],
                    avatar=agent_data['avatar'],
                    expertise=json.dumps(agent_data['expertise']),
                    thinking_style=agent_data['thinkingStyle'],
                    personality=agent_data['personality'],
                    tools=json.dumps(agent_data['tools']),
                    is_active=agent_data['isActive'],
                    is_moderator=agent_data.get('isModerator', False),
                    moderator_config=json.dumps(agent_data['moderatorConfig']) if agent_data.get('moderatorConfig') else None,
                    llm_config_id=agent_data['llmConfig']['id']
                )
                db.session.merge(agent)

        # 导入讨论
        if 'discussions' in data:
            for discussion_data in data['discussions']:
                discussion = Discussion(
                    id=discussion_data['id'],
                    topic=discussion_data['topic'],
                    mode=discussion_data['mode'],
                    participants=json.dumps(discussion_data['participants']),
                    status=discussion_data['status'],
                    consensus=discussion_data.get('consensus'),
                    consensus_score=discussion_data.get('consensusScore', 0.0)
                )
                db.session.merge(discussion)

                # 导入消息
                if 'messages' in discussion_data:
                    for message_data in discussion_data['messages']:
                        # 验证必需字段
                        if not message_data.get('agentId'):
                            logger.warning(f"跳过消息 {message_data.get('id', 'unknown')}：缺少agentId")
                            continue

                        try:
                            message = Message(
                                id=message_data['id'],
                                agent_id=message_data['agentId'],
                                discussion_id=discussion_data['id'],
                                content=message_data['content'],
                                message_type=message_data['type'],
                                reply_to=message_data.get('replyTo')
                            )
                            db.session.merge(message)
                        except ValueError as e:
                            logger.warning(f"跳过无效消息 {message_data.get('id', 'unknown')}: {str(e)}")
                            continue

        # 导入设置
        if 'settings' in data and data['settings']:
            settings_data = data['settings']
            settings = AppSettings(
                version=settings_data.get('version', '1.0.0'),
                auto_save=settings_data.get('autoSave', True),
                max_stored_discussions=settings_data.get('maxStoredDiscussions', 100),
                default_discussion_mode=settings_data.get('defaultDiscussionMode', 'free'),
                theme=settings_data.get('theme', 'light')
            )
            db.session.merge(settings)

        # 导入偏好
        if 'preferences' in data and data['preferences']:
            preferences_data = data['preferences']
            preferences = UserPreferences(
                default_agent_count=preferences_data.get('defaultAgentCount', 3),
                preferred_llm_provider=preferences_data.get('preferredLLMProvider', 'openai'),
                auto_start_discussion=preferences_data.get('autoStartDiscussion', False),
                show_advanced_options=preferences_data.get('showAdvancedOptions', False),
                notifications_enabled=preferences_data.get('notificationsEnabled', True),
                export_format=preferences_data.get('exportFormat', 'json')
            )
            db.session.merge(preferences)

        db.session.commit()

        return jsonify({'message': '数据导入成功'})
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error importing data: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/data/clear', methods=['DELETE'])
def clear_data():
    """清除所有数据"""
    try:
        Message.query.delete()
        Discussion.query.delete()
        Agent.query.delete()
        LLMConfig.query.delete()
        AppSettings.query.delete()
        UserPreferences.query.delete()

        db.session.commit()

        return jsonify({'message': '所有数据已清除'})
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error clearing data: {e}")
        return jsonify({'error': str(e)}), 500

# ============= 存储信息接口 =============

@api_bp.route('/storage/info', methods=['GET'])
def get_storage_info():
    """获取存储信息"""
    try:
        # 获取数据库文件大小
        basedir = os.path.abspath(os.path.dirname(__file__))
        db_path = os.path.join(basedir, 'multi_agent_system.db')
        
        if os.path.exists(db_path):
            used = os.path.getsize(db_path)
        else:
            used = 0
        
        # 获取可用磁盘空间
        if hasattr(os, 'statvfs'):  # Unix/Linux
            statvfs = os.statvfs(basedir)
            available = statvfs.f_bavail * statvfs.f_frsize
            total = statvfs.f_blocks * statvfs.f_frsize
        elif os.name == 'nt':  # Windows
            import shutil
            total, used_disk, available = shutil.disk_usage(basedir)
        else:
            # 默认值
            total = 1024 * 1024 * 1024  # 1GB
            available = total - used
        
        return jsonify({
            'used': used,
            'available': available,
            'total': total
        })
    except Exception as e:
        logger.error(f"Error getting storage info: {e}")
        return jsonify({'error': str(e)}), 500
