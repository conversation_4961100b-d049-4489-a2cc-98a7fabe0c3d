import React, { useState } from 'react';
import { AppProvider, useApp } from './context/AppContext';
import AgentManager from './components/AgentManager';
import DiscussionSetup from './components/DiscussionSetup';
import DiscussionRoom from './components/DiscussionRoom';
import { LLMManager } from './components/LLMManager';
import { DataManager } from './components/DataManager';
import { DiscussionHistory } from './components/DiscussionHistory';
import { LoadingScreen } from './components/LoadingScreen';
import { ErrorBoundary } from './components/ErrorBoundary';
import {
  Users,
  MessageSquare,
  Settings,
  Brain,
  Zap,
  Cpu,
  Database,
  History
} from 'lucide-react';
type View = 'home' | 'agents' | 'llm' | 'setup' | 'discussion' | 'data' | 'history';
function AppContent() {
  const { state } = useApp();
  const [currentView, setCurrentView] = useState<View>('home');

  // 如果有正在进行的讨论，自动切换到讨论室
  React.useEffect(() => {
    if (state.isDiscussionActive && state.currentDiscussion) {
      setCurrentView('discussion');
    }
  }, [state.isDiscussionActive, state.currentDiscussion]);

  // 如果正在加载，显示加载屏幕
  if (state.isLoading) {
    return <LoadingScreen />;
  }
  const renderContent = () => {
    switch (currentView) {
      case 'agents':
        return <AgentManager />;
      case 'llm':
        return <LLMManager />;
      case 'data':
        return <DataManager />;
      case 'history':
        return <DiscussionHistory />;
      case 'setup':
        return <DiscussionSetup />;
      case 'discussion':
        return <DiscussionRoom />;
      default:
        return <HomePage onNavigate={setCurrentView} />;
    }
  };
  return (
    <div className="h-screen bg-gray-50 w-full flex flex-col">
      {/* 导航栏 */}
      {currentView !== 'home' && (
        <nav className="flex-shrink-0 bg-white shadow-sm border-b border-gray-200 w-full">
          <div className="w-full px-6">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center gap-8">
                <button
                  onClick={() => setCurrentView('home')}
                  className="flex items-center gap-2 text-gray-900 hover:text-blue-600 font-medium"
                >
                  <Brain size={24} className="text-blue-600" />
                  多智能体讨论系统
                </button>
                
                <div className="flex gap-6">
                  <button
                    onClick={() => setCurrentView('agents')}
                    className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                      currentView === 'agents'
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <Users size={20} />
                    智能体管理
                  </button>

                  <button
                    onClick={() => setCurrentView('llm')}
                    className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                      currentView === 'llm'
                        ? 'bg-orange-100 text-orange-700'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <Cpu size={20} />
                    LLM管理
                  </button>

                  <button
                    onClick={() => setCurrentView('history')}
                    className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                      currentView === 'history'
                        ? 'bg-purple-100 text-purple-700'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <History size={20} />
                    讨论历史
                  </button>

                  <button
                    onClick={() => setCurrentView('data')}
                    className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                      currentView === 'data'
                        ? 'bg-indigo-100 text-indigo-700'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    <Database size={20} />
                    数据管理
                  </button>

                  <button
                    onClick={() => setCurrentView('setup')}
                    className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                      currentView === 'setup'
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                    disabled={state.agents.length === 0}
                  >
                    <Settings size={20} />
                    创建讨论
                  </button>
                  
                  {state.isDiscussionActive && (
                    <button
                      onClick={() => setCurrentView('discussion')}
                      className={`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${
                        currentView === 'discussion' 
                          ? 'bg-green-100 text-green-700' 
                          : 'text-green-600 hover:text-green-700'
                      }`}
                    >
                      <MessageSquare size={20} />
                      讨论进行中
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    </button>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-500">
                  {state.agents.length} 个智能体
                </span>
                {state.isDiscussionActive && (
                  <div className="flex items-center gap-2 bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    讨论中
                  </div>
                )}
              </div>
            </div>
          </div>
        </nav>
      )}

      {/* 主内容 */}
      <div className="flex-1 overflow-hidden">
        {renderContent()}
      </div>
    </div>
  );
}
function HomePage({ onNavigate }: { onNavigate: (view: View) => void }) {
  const { state } = useApp();
  return (
    <div className="h-full bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 w-full fixed-layout overflow-y-auto">
      <div className="w-full px-6 py-12 fixed-layout">
        
        {/* 头部介绍 */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-6">
            <Brain size={48} className="text-blue-600" />
            <h1 className="text-5xl font-bold text-gray-900">多智能体讨论系统</h1>
          </div>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            通过配置不同专业背景和思维方式的AI智能体，创建富有洞察力的讨论环境，
            探索复杂问题的多维度解决方案，并达成有价值的共识。
          </p>
          
          <div className="flex items-center justify-center gap-8 mt-8 text-sm text-gray-500">
            <div className="flex items-center gap-2">
              <Zap size={16} className="text-yellow-500" />
              支持2-8个智能体同时讨论
            </div>
            <div className="flex items-center gap-2">
              <Brain size={16} className="text-purple-500" />
              智能共识判断算法
            </div>
            <div className="flex items-center gap-2">
              <MessageSquare size={16} className="text-blue-500" />
              实时讨论模拟
            </div>
          </div>
        </div>
        {/* 功能卡片 */}
        <div className="space-y-8 mb-16">
          {/* 第一排：智能体管理、LLM管理、数据管理 */}
          <div className="flex flex-wrap justify-center gap-6">
            {/* 智能体管理 */}
            <div className="group">
              <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-blue-200 fixed-size-card">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                    <Users size={32} className="text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">智能体管理</h3>
                    <p className="text-gray-500">配置AI智能体</p>
                  </div>
                </div>

                <p className="text-gray-600 mb-6 leading-relaxed">
                  创建和配置具有不同专业背景、思维方式和性格特征的智能体。
                  每个智能体都有独特的知识领域和讨论风格。
                </p>

                <div className="mb-6">
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                    <span>当前智能体数量</span>
                    <span className="font-bold text-blue-600 text-lg">{state.agents.length}/8</span>
                  </div>
                  <div className="w-full h-2 bg-gray-200 rounded-full">
                    <div
                      className="h-full bg-blue-500 rounded-full transition-all"
                      style={{ width: `${(state.agents.length / 8) * 100}%` }}
                    />
                  </div>
                </div>

                <button
                  onClick={() => onNavigate('agents')}
                  className="w-full bg-blue-600 text-white py-3 rounded-xl hover:bg-blue-700 transition-colors font-medium"
                >
                  管理智能体
                </button>
              </div>
            </div>

            {/* LLM管理 */}
            <div className="group">
              <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-orange-200 fixed-size-card">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center group-hover:bg-orange-200 transition-colors">
                    <Cpu size={32} className="text-orange-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">LLM管理</h3>
                    <p className="text-gray-500">配置大语言模型</p>
                  </div>
                </div>

                <p className="text-gray-600 mb-6 leading-relaxed">
                  配置和管理大语言模型，为智能体提供真实的AI对话能力。
                  支持OpenAI、Anthropic等多种提供商。
                </p>

                <div className="space-y-3 mb-6">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    支持多种LLM提供商
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    个性化配置参数
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    连接测试功能
                  </div>
                </div>

                <button
                  onClick={() => onNavigate('llm')}
                  className="w-full bg-orange-600 text-white py-3 rounded-xl hover:bg-orange-700 transition-colors font-medium"
                >
                  管理LLM配置
                </button>
              </div>
            </div>

            {/* 数据管理 */}
            <div className="group">
              <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-indigo-200 fixed-size-card">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 bg-indigo-100 rounded-xl flex items-center justify-center group-hover:bg-indigo-200 transition-colors">
                    <Database size={32} className="text-indigo-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">数据管理</h3>
                    <p className="text-gray-500">备份与恢复</p>
                  </div>
                </div>

                <p className="text-gray-600 mb-6 leading-relaxed">
                  管理系统数据的备份、恢复和清理，确保配置和历史记录的安全。
                </p>

                <div className="space-y-3 mb-6">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    数据导出备份
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    配置导入恢复
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    数据清理重置
                  </div>
                </div>

                <button
                  onClick={() => onNavigate('data')}
                  className="w-full bg-indigo-600 text-white py-3 rounded-xl hover:bg-indigo-700 transition-colors font-medium"
                >
                  管理数据
                </button>
              </div>
            </div>
          </div>

          {/* 第二排：创建讨论、讨论状态、讨论历史 */}
          <div className="flex flex-wrap justify-center gap-6">
            {/* 创建讨论 */}
            <div className="group">
              <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-purple-200 fixed-size-card">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                    <Settings size={32} className="text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">创建讨论</h3>
                    <p className="text-gray-500">配置讨论参数</p>
                  </div>
                </div>

                <p className="text-gray-600 mb-6 leading-relaxed">
                  设置讨论话题、选择参与的智能体、配置讨论模式，
                  开始一场富有见解的AI讨论。
                </p>

                <div className="space-y-3 mb-6">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    自由讨论模式
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    主持人模式
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    智能共识判断
                  </div>
                </div>

                <button
                  onClick={() => onNavigate('setup')}
                  disabled={state.agents.length < 2}
                  className="w-full bg-purple-600 text-white py-3 rounded-xl hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium"
                >
                  {state.agents.length < 2 ? '需要至少2个智能体' : '创建新讨论'}
                </button>
              </div>
            </div>

            {/* 讨论状态 */}
            <div className="group">
              <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-green-200 fixed-size-card">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center group-hover:bg-green-200 transition-colors">
                    <MessageSquare size={32} className="text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">讨论状态</h3>
                    <p className="text-gray-500">实时监控</p>
                  </div>
                </div>

                {state.isDiscussionActive ? (
                  <>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      当前有一场讨论正在进行中，您可以实时观看智能体之间的对话，
                      监控共识度变化。
                    </p>

                    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span className="font-medium text-green-800">讨论进行中</span>
                      </div>
                      <p className="text-green-700 text-sm">
                        话题：{state.currentDiscussion?.topic}
                      </p>
                    </div>

                    <button
                      onClick={() => onNavigate('discussion')}
                      className="w-full bg-green-600 text-white py-3 rounded-xl hover:bg-green-700 transition-colors font-medium"
                    >
                      进入讨论室
                    </button>
                  </>
                ) : (
                  <>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      目前没有进行中的讨论。创建智能体并配置讨论参数后，
                      您就可以开始一场精彩的AI对话。
                    </p>

                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                        <span className="font-medium text-gray-600">空闲状态</span>
                      </div>
                      <p className="text-gray-500 text-sm">
                        历史讨论：{state.allDiscussions.length} 场
                      </p>
                    </div>

                    <button
                      onClick={() => onNavigate(state.agents.length < 2 ? 'agents' : 'setup')}
                      className="w-full bg-gray-400 text-white py-3 rounded-xl hover:bg-gray-500 transition-colors font-medium"
                    >
                      {state.agents.length < 2 ? '先创建智能体' : '开始新讨论'}
                    </button>
                  </>
                )}
              </div>
            </div>

            {/* 讨论历史 */}
            <div className="group">
              <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-purple-200 fixed-size-card">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                    <History size={32} className="text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">讨论历史</h3>
                    <p className="text-gray-500">查看历史记录</p>
                  </div>
                </div>

                <p className="text-gray-600 mb-6 leading-relaxed">
                  查看和分析历史讨论记录，了解智能体的对话模式和共识形成过程。
                </p>

                <div className="mb-6">
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                    <span>历史讨论数量</span>
                    <span className="font-bold text-purple-600 text-lg">{state.allDiscussions.length}</span>
                  </div>
                  <div className="w-full h-2 bg-gray-200 rounded-full">
                    <div
                      className="h-full bg-purple-500 rounded-full transition-all"
                      style={{ width: `${Math.min((state.allDiscussions.length / 10) * 100, 100)}%` }}
                    />
                  </div>
                </div>

                <button
                  onClick={() => onNavigate('history')}
                  className="w-full bg-purple-600 text-white py-3 rounded-xl hover:bg-purple-700 transition-colors font-medium"
                >
                  查看历史
                </button>
              </div>
            </div>
          </div>
        </div>
        {/* 特色介绍 */}
        <div className="bg-white rounded-2xl shadow-xl p-12 border border-gray-100">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-8">
            系统特色功能
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Brain size={32} className="text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">智能化对话</h3>
              <p className="text-gray-600 text-sm">基于专业领域和性格特征生成真实的对话内容</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Zap size={32} className="text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">实时共识监控</h3>
              <p className="text-gray-600 text-sm">动态计算讨论共识度，智能判断达成一致的时机</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Users size={32} className="text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">多模式讨论</h3>
              <p className="text-gray-600 text-sm">支持自由讨论和主持人模式，适应不同讨论需求</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <MessageSquare size={32} className="text-orange-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">丰富的交互</h3>
              <p className="text-gray-600 text-sm">支持多种消息类型，包括陈述、提问、同意、反对</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
export default function App() {
  return (
    <ErrorBoundary>
      <AppProvider>
        <AppContent />
      </AppProvider>
    </ErrorBoundary>
  );
}